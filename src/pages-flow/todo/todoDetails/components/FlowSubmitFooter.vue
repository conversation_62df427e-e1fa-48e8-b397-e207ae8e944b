<script lang="ts" setup>
defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})

const props = defineProps({
  // 表单基础设置
  formBaseInfo: {
    type: Object,
    default: () => ({}),
  },
})
const emit = defineEmits(['change'])
function onClick(type) {
  emit('change', type)
}

const jobBtns = computed(() => {
  return props.formBaseInfo?.jobBtns || []
})
</script>

<template>
  <view class="flow-submit-footer">
    <wd-button v-if="jobBtns.includes('10')" type="info" plain @click="onClick('backPre')">
      退回
    </wd-button>
    <wd-button v-if="jobBtns.includes('18')" type="error" @click="onClick('reject')">
      拒绝
    </wd-button>
    <wd-button v-if="jobBtns.includes('0')" type="primary" @click="onClick('agree')">
      同意
    </wd-button>
  </view>
</template>

<style lang="scss" scoped>
.flow-submit-footer {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(0, 1fr));
  gap: 12rpx;
  width: 100%;
  background-color: #fff;
  padding: 20rpx 20rpx calc(20rpx + env(safe-area-inset-bottom));
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 66;
  box-sizing: border-box;
  :deep(.wd-button) {
    height: 85rpx;
    width: 100%;
    min-width: 0;
    max-width: 100%;
    box-sizing: border-box;
    overflow: hidden;
  }
}
</style>
