// 测试 dayjs 日期转换函数
import dayjs from 'dayjs'

// 使用 dayjs 进行日期格式转换
function formatDateToString(timestamp) {
  if (!timestamp) return ''
  return dayjs(timestamp).format('YYYY-MM-DD')
}

function parseDateString(dateStr) {
  if (!dateStr) return null
  const parsed = dayjs(dateStr, 'YYYY-MM-DD')
  return parsed.isValid() ? parsed.valueOf() : null
}

// 测试用例
console.log('=== 测试 dayjs 日期转换 ===')

// 测试时间戳转字符串
const timestamp = Date.now()
console.log('当前时间戳:', timestamp)
console.log('转换为 YYYY-MM-DD:', formatDateToString(timestamp))

// 测试字符串转时间戳
const dateStr = '2024-12-25'
console.log('日期字符串:', dateStr)
console.log('转换为时间戳:', parseDateString(dateStr))

// 测试往返转换
const originalTimestamp = new Date('2024-12-25').getTime()
const converted = formatDateToString(originalTimestamp)
const backToTimestamp = parseDateString(converted)
console.log('原始时间戳:', originalTimestamp)
console.log('转换为字符串:', converted)
console.log('转换回时间戳:', backToTimestamp)
console.log('往返转换是否一致:', originalTimestamp === backToTimestamp)

// 测试无效输入
console.log('空值测试:', formatDateToString(null))
console.log('无效日期测试:', parseDateString('invalid-date'))
console.log('空字符串测试:', parseDateString(''))
