<template>
  <view class="test-container">
    <text class="title">测试 Calendar 组件替换</text>
    
    <!-- 测试基本日期选择 -->
    <view class="test-section">
      <text class="section-title">基本日期选择</text>
      <FlowFormRendering
        :rules="basicDatePickerRules"
        :model-value="basicDateModel"
        @update:model-value="basicDateModel = $event"
      />
      <text class="result">选中值: {{ JSON.stringify(basicDateModel) }}</text>
    </view>

    <!-- 测试月份选择 -->
    <view class="test-section">
      <text class="section-title">月份选择</text>
      <FlowFormRendering
        :rules="monthPickerRules"
        :model-value="monthModel"
        @update:model-value="monthModel = $event"
      />
      <text class="result">选中值: {{ JSON.stringify(monthModel) }}</text>
    </view>

    <!-- 测试日期范围选择 -->
    <view class="test-section">
      <text class="section-title">日期范围选择</text>
      <FlowFormRendering
        :rules="dateRangePickerRules"
        :model-value="dateRangeModel"
        @update:model-value="dateRangeModel = $event"
      />
      <text class="result">选中值: {{ JSON.stringify(dateRangeModel) }}</text>
    </view>

    <!-- 测试时间选择器（应该继续使用 datetime-picker） -->
    <view class="test-section">
      <text class="section-title">时间选择器（应该继续使用 datetime-picker）</text>
      <FlowFormRendering
        :rules="timePickerRules"
        :model-value="timeModel"
        @update:model-value="timeModel = $event"
      />
      <text class="result">选中值: {{ JSON.stringify(timeModel) }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import FlowFormRendering from './src/pages-flow/components/form-create/flow-form-rendering/flow-form-rendering.vue'

// 基本日期选择测试数据
const basicDateModel = ref({ basicDate: '2024-12-25' }) // 测试初始值
const basicDatePickerRules = {
  widgetList: [
    {
      _fc_id: 'test1',
      type: 'datePicker',
      field: 'basicDate',
      title: '基本日期选择',
      props: {
        placeholder: '请选择日期'
      },
      $required: true
    }
  ]
}

// 月份选择测试数据
const monthModel = ref({})
const monthPickerRules = {
  widgetList: [
    {
      _fc_id: 'test2',
      type: 'datePicker',
      field: 'monthDate',
      title: '月份选择',
      props: {
        picker: 'month',
        placeholder: '请选择月份'
      },
      $required: true
    }
  ]
}

// 日期范围选择测试数据
const dateRangeModel = ref({ dateRange: ['2024-12-20', '2024-12-30'] }) // 测试初始值
const dateRangePickerRules = {
  widgetList: [
    {
      _fc_id: 'test3',
      type: 'datePicker',
      field: 'dateRange',
      title: '日期范围选择',
      props: {
        range: true,
        placeholder: '请选择日期范围'
      },
      $required: true
    }
  ]
}

// 时间选择器测试数据
const timeModel = ref({})
const timePickerRules = {
  widgetList: [
    {
      _fc_id: 'test4',
      type: 'timePicker',
      field: 'time',
      title: '时间选择',
      props: {
        placeholder: '请选择时间'
      },
      $required: true
    }
  ]
}
</script>

<style scoped>
.test-container {
  padding: 20rpx;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 40rpx;
  display: block;
}

.test-section {
  margin-bottom: 60rpx;
  padding: 20rpx;
  border: 1px solid #eee;
  border-radius: 8rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  display: block;
  color: #333;
}

.result {
  margin-top: 20rpx;
  padding: 10rpx;
  background-color: #f5f5f5;
  border-radius: 4rpx;
  font-size: 24rpx;
  display: block;
  word-break: break-all;
}
</style>
